from functools import wraps

def validate_token(token: str):
    # 这里写你的token校验逻辑，比如解密、查数据库等
    # 示例：假设token为"valid-token"时通过
    if token == "valid-token":
        return {"user_id": "123", "permissions": ["get_month_report"]}
    return None

def require_token(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 假设FastMCP的tool支持context参数
        context = kwargs.get("context")
        if not context:
            raise Exception("缺少context，无法获取header")
        token = context.headers.get("Authorization")
        if not token:
            raise Exception("未提供token")
        # 去掉Bearer前缀
        if token.startswith("Bearer "):
            token = token[7:]
        user = validate_token(token)
        if not user:
            raise Exception("无效token")
        kwargs["user"] = user  # 注入user信息
        return await func(*args, **kwargs)
    return wrapper