# -*- coding: utf-8 -*-

"""
动态环境配置文件
根据 config/env.py 中的设置自动切换环境配置
"""

from .env import get_current_config

# 获取当前环境配置
current_env_config = get_current_config()

# ======================================================================================================================
# 服务启动配置
# ======================================================================================================================
SERVER_CONFIG = {
    "host": "0.0.0.0",  # 监听地址
    "port": 8010,       # 监听端口
    "debug": current_env_config["debug"],      # 调试模式 - 根据环境动态设置
    "workers": current_env_config["workers"], # 工作进程数 - 根据环境动态设置
    "access_log": None, # 访问日志格式
}


# ======================================================================================================================
# 外部系统接口配置
# ======================================================================================================================
EXTERNAL_SYSTEMS = {
    "base_url": current_env_config["base_url"],  # API地址 - 根据环境动态设置
    "appkey": current_env_config["appkey"],
    "headers": {
        'Accept': 'application/json, text/plain, */*',
        'Authorization': '',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.5845.97 Safari/537.36 SE 2.X MetaSr 1.0',
    },
    "timeout": 30, # 请求超时时间（秒）
    "report": {
        "api": {
            "get_month_report": "kq-api/system/month_report/list",   # 月度报表
            "get_attendance_list": "kq-api/system/clock/myList",     # 考勤列表
            "get_leave_list": "kq-api/system/leave/myList",          # 请假列表
            "get_trip_list": "kq-api/system/trip/list",                   # 出差列表
            "get_businessout_list": "kq-api/system/away/list",            # 公务外出列表
            "get_replenish_list": "kq-api/system/replenish/myList",       # 补卡列表
            "get_leave_type_list": "kq-api/system/leave/leaveType/list",   # 请假类型列表
            "get_process_type_list": "kq-api/system/leave/processTypeDesc/list",   # 请假流程类型列表
            "lack_list": "kq-api/system/clock/lackList",   # 查询指定日期缺卡列表
            "post_leave": "/leave",   # 发起请假
            "get_daily_report": "kq-api/system/admin/daily_report/getList",  # 查询指定日期的考勤详情
            "vacation": "worker/#/vacation",   # 请假前端页面接口，
            "evection": "worker/#/evection",   # 出差前端页面接口， 
            "businessOut": "worker/#/publicity",   # 公务外出前端页面接口，
            "replenish": "worker/#/remedy",       # 补卡前端页面接口，
            "token": "kq-api/auth/aiLogin"   # 获取token
        }
    }
}



# ======================================================================================================================
# 主配置对象 (方便统一导入和管理)
# ======================================================================================================================
APP_CONFIG = {
    "server": SERVER_CONFIG,
    "external": EXTERNAL_SYSTEMS,
    # 在这里可以添加更多配置组
} 