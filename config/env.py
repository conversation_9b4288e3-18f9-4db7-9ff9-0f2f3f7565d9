# -*- coding: utf-8 -*-

"""
环境配置文件
只需要修改下面的 CURRENT_ENV 变量就可以切换环境
支持的环境：local, uat, prod
"""

# ========================================
# 当前环境设置 - 只需要修改这里！
# ========================================
CURRENT_ENV = "local"  # 可选值: "local", "uat", "prod"

# 环境配置映射
ENV_CONFIGS = {
    "local": {
        "name": "本地开发环境",
        "debug": True,
        "workers": 1,
        "base_url": "http://192.168.251.68/",
        "appkey": "kaoqinxtAiSecret"
    },
    "uat": {
        "name": "UAT测试环境", 
        "debug": False,
        "workers": 2,
        "base_url": "http://uat-api.example.com/",
        "appkey": "kaoqinxtAiSecret"
    },
    "prod": {
        "name": "生产环境",
        "debug": False,
        "workers": 4,
        "base_url": "https://work.c-land.com/",
        "appkey": "kaoqinxtAiSecret"
    }
}

# 获取当前环境配置
def get_current_config():
    if CURRENT_ENV not in ENV_CONFIGS:
        print(f"⚠️  警告: 不支持的环境 '{CURRENT_ENV}'，将使用默认的 'local' 环境")
        return ENV_CONFIGS["local"]
    
    config = ENV_CONFIGS[CURRENT_ENV]
    print(f"✅ 当前环境: {config['name']} ({CURRENT_ENV})")
    return config 