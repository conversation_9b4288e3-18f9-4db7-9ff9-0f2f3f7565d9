attendance = {
	"total": 146,
	"rows": [
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-13 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-14 00:02:14",
			"remark": None,
			"params": {},
			"id": 280635,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-13",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-13 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-13 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-13 08:31:00",
			"missingMinuteBeginTime": "2025-06-13 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-13 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-13 17:30:00",
			"normalMinuteEndTime": "2025-06-14 05:29:00",
			"abnormalMinuteEndTime": "2025-06-13 17:29:00",
			"missingMinuteEndTime": "2025-06-13 17:00:00",
			"missingDayMinuteEndTime": "2025-06-13 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749792600000, \"beginDateTime\": 1749787200000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-13T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-13T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-13 星期五",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-12 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-13 00:02:12",
			"remark": None,
			"params": {},
			"id": 279674,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-12",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-12 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-12 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-12 08:31:00",
			"missingMinuteBeginTime": "2025-06-12 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-12 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-12 17:30:00",
			"normalMinuteEndTime": "2025-06-13 05:29:00",
			"abnormalMinuteEndTime": "2025-06-12 17:29:00",
			"missingMinuteEndTime": "2025-06-12 17:00:00",
			"missingDayMinuteEndTime": "2025-06-12 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749706200000, \"beginDateTime\": 1749700800000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-12T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-12T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-12 星期四",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-11 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-12 00:02:10",
			"remark": None,
			"params": {},
			"id": 278713,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-11",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-11 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-11 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-11 08:31:00",
			"missingMinuteBeginTime": "2025-06-11 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-11 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-11 17:30:00",
			"normalMinuteEndTime": "2025-06-12 05:29:00",
			"abnormalMinuteEndTime": "2025-06-11 17:29:00",
			"missingMinuteEndTime": "2025-06-11 17:00:00",
			"missingDayMinuteEndTime": "2025-06-11 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749619800000, \"beginDateTime\": 1749614400000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-11T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-11T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-11 星期三",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-10 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-11 00:02:13",
			"remark": None,
			"params": {},
			"id": 277752,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-10",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-10 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-10 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-10 08:31:00",
			"missingMinuteBeginTime": "2025-06-10 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-10 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-10 17:30:00",
			"normalMinuteEndTime": "2025-06-11 05:29:00",
			"abnormalMinuteEndTime": "2025-06-10 17:29:00",
			"missingMinuteEndTime": "2025-06-10 17:00:00",
			"missingDayMinuteEndTime": "2025-06-10 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749533400000, \"beginDateTime\": 1749528000000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-10T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-10T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-10 星期二",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-09 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-10 00:02:12",
			"remark": None,
			"params": {},
			"id": 276791,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-09",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-09 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-09 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-09 08:31:00",
			"missingMinuteBeginTime": "2025-06-09 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-09 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-09 17:30:00",
			"normalMinuteEndTime": "2025-06-10 05:29:00",
			"abnormalMinuteEndTime": "2025-06-09 17:29:00",
			"missingMinuteEndTime": "2025-06-09 17:00:00",
			"missingDayMinuteEndTime": "2025-06-09 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749447000000, \"beginDateTime\": 1749441600000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-09T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-09T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-09 星期一",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-06 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-07 00:02:16",
			"remark": None,
			"params": {},
			"id": 275544,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-06",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-06 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-06 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-06 08:31:00",
			"missingMinuteBeginTime": "2025-06-06 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-06 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-06 17:30:00",
			"normalMinuteEndTime": "2025-06-07 05:29:00",
			"abnormalMinuteEndTime": "2025-06-06 17:29:00",
			"missingMinuteEndTime": "2025-06-06 17:00:00",
			"missingDayMinuteEndTime": "2025-06-06 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749187800000, \"beginDateTime\": 1749182400000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-06T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-06T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-06 星期五",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-05 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-06 00:02:15",
			"remark": None,
			"params": {},
			"id": 274583,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-05",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-05 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-05 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-05 08:31:00",
			"missingMinuteBeginTime": "2025-06-05 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-05 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-05 17:30:00",
			"normalMinuteEndTime": "2025-06-06 05:29:00",
			"abnormalMinuteEndTime": "2025-06-05 17:29:00",
			"missingMinuteEndTime": "2025-06-05 17:00:00",
			"missingDayMinuteEndTime": "2025-06-05 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749101400000, \"beginDateTime\": 1749096000000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-05T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-05T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-05 星期四",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-04 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-05 00:02:11",
			"remark": None,
			"params": {},
			"id": 273622,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-04",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-04 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-04 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-04 08:31:00",
			"missingMinuteBeginTime": "2025-06-04 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-04 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-04 17:30:00",
			"normalMinuteEndTime": "2025-06-05 05:29:00",
			"abnormalMinuteEndTime": "2025-06-04 17:29:00",
			"missingMinuteEndTime": "2025-06-04 17:00:00",
			"missingDayMinuteEndTime": "2025-06-04 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1749015000000, \"beginDateTime\": 1749009600000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-04T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-04T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-04 星期三",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-03 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-04 00:02:10",
			"remark": None,
			"params": {},
			"id": 272660,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-03",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-03 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-03 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-03 08:31:00",
			"missingMinuteBeginTime": "2025-06-03 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-03 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-03 17:30:00",
			"normalMinuteEndTime": "2025-06-04 05:29:00",
			"abnormalMinuteEndTime": "2025-06-03 17:29:00",
			"missingMinuteEndTime": "2025-06-03 17:00:00",
			"missingDayMinuteEndTime": "2025-06-03 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1748928600000, \"beginDateTime\": 1748923200000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-03T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-03T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-03 星期二",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		},
		{
			"searchValue": None,
			"createBy": "",
			"createTime": "2025-06-02 00:00:00",
			"updateBy": "",
			"updateTime": "2025-06-03 00:02:13",
			"remark": None,
			"params": {},
			"id": 271699,
			"userId": 1,
			"nickName": None,
			"checkinDate": "2025-06-02",
			"status": 1,
			"shiftId": 8,
			"groupId": 14,
			"shiftDetailId": 153,
			"shiftBeginTime": "2025-06-02 08:30:00",
			"beginRecordId": 0,
			"normalMinuteBeginTime": "2025-06-02 05:30:00",
			"abnormalMinuteBeginTime": "2025-06-02 08:31:00",
			"missingMinuteBeginTime": "2025-06-02 09:00:00",
			"missingDayMinuteBeginTime": "2025-06-02 10:30:00",
			"beginStatus": 3,
			"shiftEndTime": "2025-06-02 17:30:00",
			"normalMinuteEndTime": "2025-06-03 05:29:00",
			"abnormalMinuteEndTime": "2025-06-02 17:29:00",
			"missingMinuteEndTime": "2025-06-02 17:00:00",
			"missingDayMinuteEndTime": "2025-06-02 15:30:00",
			"endStatus": 3,
			"endRecordId": 0,
			"shouldWorkLength": 450.0,
			"workLength": 0.0,
			"absenceLength": 450.0,
			"delFlag": 0,
			"checkinStartTime": None,
			"checkinEndTime": None,
			"auditStatus": None,
			"shiftRestRules": "[{\"endTime\": \"13:30\", \"endType\": 1, \"beginTime\": \"12:00\", \"beginType\": 1, \"endDateTime\": 1748842200000, \"beginDateTime\": 1748836800000}]",
			"deptIds": None,
			"shiftRestRulesObj": [
				{
					"beginType": 1,
					"beginTime": "12:00",
					"beginDateTime": "2025-06-02T12:00:00.000+08:00",
					"endType": 1,
					"endTime": "13:30",
					"endDateTime": "2025-06-02T13:30:00.000+08:00"
				}
			],
			"deptId": None,
			"beginAndEndStatus": None,
			"groupSystemType": None,
			"clockRecordList": None,
			"statusDesc": "异常",
			"checkinDateDesc": "2025-06-02 星期一",
			"clockBeginTime": None,
			"clockBeginStatus": None,
			"clockBeginStatusDesc": None,
			"clockBeginScopeType": None,
			"clockBeginReplenishStatus": None,
			"clockBeginReplenishStatusDesc": None,
			"clockEndTime": None,
			"clockEndStatus": None,
			"clockEndStatusDesc": None,
			"clockEndScopeType": None,
			"clockEndReplenishStatus": None,
			"clockEndReplenishStatusDesc": None,
			"replenishStatusDesc": ""
		}
	],
	"code": 200,
	"msg": "查询成功"
}
