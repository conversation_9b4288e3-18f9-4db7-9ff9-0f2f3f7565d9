def main():
  import datetime
  """
  获取当前日期信息，并将本周一至周五、上周一、下周一的日期作为独立的键值对返回。
  这种“扁平化”的结构能更好地兼容Dify的变量引用。

  Returns:
   dict: 包含多个独立日期键的字典。
         例如: {
             'today': '2025-07-21',
             'day_of_week': '星期一',
             'monday_date': '2025-07-21',
             'tuesday_date': '2025-07-22',
             'wednesday_date': '2025-07-23',
             'thursday_date': '2025-07-24',
             'friday_date': '2025-07-25',
             'last_monday_date': '2025-07-14',
             'next_monday_date': '2025-07-28',
             ...其他信息...
         }
  """
  # 获取当前日期
  today_date = datetime.date.today()
  weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
  
  # 定位到本周一
  this_monday = today_date - datetime.timedelta(days=today_date.weekday())

  # --- 返回一个扁平化的字典 ---
  # 不再使用嵌套结构，而是为每个日期创建一个独立的顶层键

  return {
      # 基础信息
      'today': today_date.strftime('%Y-%m-%d'),
      'current_year': today_date.year,
      'current_month': today_date.month,
      'iso_week_number': today_date.isocalendar()[1],
      'day_of_week': weekdays[today_date.weekday()],
      
      # 本周工作日（作为独立键）
      'monday_date': this_monday.strftime('%Y-%m-%d'),
      'tuesday_date': (this_monday + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
      'wednesday_date': (this_monday + datetime.timedelta(days=2)).strftime('%Y-%m-%d'),
      'thursday_date': (this_monday + datetime.timedelta(days=3)).strftime('%Y-%m-%d'),
      'friday_date': (this_monday + datetime.timedelta(days=4)).strftime('%Y-%m-%d'),

      # 上周和下周一（作为独立键）
      'last_monday_date': (this_monday - datetime.timedelta(weeks=1)).strftime('%Y-%m-%d'),
      'next_monday_date': (this_monday + datetime.timedelta(weeks=1)).strftime('%Y-%m-%d'),
  }