# schemas/attendance.py
from pydantic import BaseModel
from typing import List, Optional

class AttendanceColumn(BaseModel):
    key: str
    title: str

class AttendanceRow(BaseModel):
    date: str
    clock_in: Optional[str]
    clock_out: Optional[str]
    absence_length: str
    status: str
    replenish_status: str
    remark: str

class AttendanceListResponse(BaseModel):
    columns: List[AttendanceColumn]
    data: List[AttendanceRow]