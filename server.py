# 文件名：server.py
from fastmcp import FastMCP
from core.mcp_instance import fast_mcp
import asyncio
import tools.query.leave
import tools.query.attendance
import tools.leave
import tools.common
import tools.trip
import tools.businessOut
import tools.replenish
import tools.query.businessout
import tools.query.trip
import tools.query.replenish
from config.config import SERVER_CONFIG

@fast_mcp.tool('get_weather')
def get_weather_tool(city: str):
    """查询指定城市的当前天气"""
    if not isinstance(city, str):
        raise ValueError("城市名称必须为字符串")
    
    fake_weather_data = {
        "北京": {"temperature": "15°C", "condition": "晴"},
        "上海": {"temperature": "18°C", "condition": "多云"},
    }

    weather = fake_weather_data.get(city, {"temperature": "未知", "condition": "数据未收录"})
    return {"city": city, **weather}

@fast_mcp.tool('calculator')
def calculator(a: float, b: float, operator: str):
    """
    执行基本的数学运算。
    支持的运算符: +, -, *, /
    """
    if operator == "+":
        return a + b
    elif operator == "-":
        return a - b
    elif operator == "*":
        return a * b
    elif operator == "/":
        if b == 0:
            raise ValueError("除数不能为0")
        return a / b
    else:
        raise ValueError("无效的运算符")


if __name__ == "__main__":
    print("FastMCP 服务已启动")
    fast_mcp.run(transport="sse", host=SERVER_CONFIG["host"], port=SERVER_CONFIG["port"])
