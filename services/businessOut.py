from utils.tool import get_url_and_headers
from utils.tool import split_datatime_str
import json
from urllib.parse import quote

async def submit_and_get_businessOut_link(businessOut_data: dict) -> str:
    
    outList = businessOut_data.get('outList')
    trips = []
    for trip in outList:
        startTime, startTimeType = split_datatime_str(trip.get('startTime'))
        endTime, endTimeType = split_datatime_str(trip.get('endTime'))
        trips.append({
            "startTime": startTime,
            "startTimeType": startTimeType,
            "endTime": endTime,
            "endTimeType": endTimeType,
            "destination": trip.get('destination'),
        })
    trips = json.dumps(trips)
    reason = businessOut_data.get('reason')
    trips = quote(trips)
    reason = quote(reason)
    url, headers = await get_url_and_headers("businessOut")
    token = headers.get("Authorization").split(" ")[1]
    complete_url = f"{url}?token={token}&zjBizTripDetailList={trips}&reason={reason}"
    print(complete_url)
    return complete_url