import httpx
from utils.tool import get_url_and_headers
from urllib.parse import quote
from utils.tool import time_format

# 处理数据
def process_leave_type_data(raw_data: dict):
    # 只保留 data 数组中每个对象的 id、balanceDesc、balance 和 dictLabel 字段
    data = raw_data.get('data', [])
    return [
        {
            'balanceDesc': item.get('balanceDesc'),
            'balance': item.get('balance'),
            'leaveTypeDesc': item.get('dictLabel')
        }
        for item in data
    ]

# 获取请假类型列表
async def get_leave_type_list():
    url, headers = await get_url_and_headers("get_leave_type_list")
    
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            return process_leave_type_data(response.json())
    except Exception as e:
        return {"error": str(e)}
    
# 处理请假流程类型列表数据
def process_process_type_data(raw_data: dict):
    # 只保留 data 数组中每个对象的 dictValue 字段
    data = raw_data.get('data', [])
    return [
        {'processTypeDesc': item.get('dictValue')} for item in data
    ]

# 获取请假流程类型列表
async def get_process_type_list():
    url, headers = await get_url_and_headers("get_process_type_list")
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            raw_data = response.json()
            return process_process_type_data(raw_data)
    except Exception as e:
        return {"error": str(e)}
    

# 获取请求数据
async def get_request_data(path: str):
    url, headers = await get_url_and_headers(path)
    try:    
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            return response.json().get('data', [])
    except Exception as e:
        return {"error": str(e)}


# 提交请假申请
async def post_leave(leave_data: dict):
    return {}
    data = await get_request_data("get_leave_type_list")
    # 将leave_data中的leaveTypeDesc字段转换为leaveType，获取数组元素中与leaveTypeDesc字段相等的元素的id 
    leave_type_id = next((item.get('id') for item in data if item.get('dictLabel') == leave_data.get('leaveTypeDesc')), None)
    leave_data['leaveType'] = leave_type_id

    url, headers = await get_url_and_headers("post_leave")
    headers = headers.copy()

    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.post(url, headers=headers, json=leave_data)
            response.raise_for_status()
            json_data = response.json()
            print(json_data)
            return json_data
    except Exception as e:
        return {"error": str(e)}
    

# 根据前端传入的参数,生成完整的请假URL
async def submit_and_get_link(leave_data: dict):
    # 参数完整性校验，缺失的参数补None
    required_fields = [
        'leaveTypeDesc', 'startTime', 'endTime',
        'reason'
    ]   

    for field in required_fields:
        if field not in leave_data:
            leave_data[field] = ""

    leaveTypeDesc = leave_data.get('leaveTypeDesc')
    
    data = await get_request_data("get_leave_type_list")
    leave_type_id = next((item.get('id') for item in data if item.get('dictLabel') == leave_data.get('leaveTypeDesc')), None)
    #startTime 和 endTime 格式为 'YYYY-MM-DD HH:MM:SS'
    #将startTime分为date和time两部分，分别赋值给start_time和start_time_type
    start_time = leave_data['startTime']
    end_time = leave_data['endTime']

    start_date = start_time.split(' ')[0]
    if len(start_time.split(' ')) == 2: 
        start_time_type = time_format(start_time.split(' ')[1])
    else:
        start_time_type = "00:00:00"      #哺乳假
        start_time = f"{start_date} 00:00:00"
    #将endTime分为date和time两部分，分别赋值给end_time和end_time_ty pe

    end_date = end_time.split(' ')[0]
    if len(end_time.split(' ')) == 2:
        end_time_type = time_format(end_time.split(' ')[1])
    else:
        end_time_type = "00:00:00"
        end_time = f"{end_date} 00:00:00"

    url, headers = await get_url_and_headers("vacation")
    token = headers.get("Authorization").split(" ")[1]
    #将reason进行urlencode
    reason = quote(leave_data['reason'])
    if leaveTypeDesc != "哺乳假" and leaveTypeDesc != "调休假":
        complete_url = f"{url}?token={token}&leaveType={leave_type_id}&startTime={start_date}&startTimeType={start_time_type}&endTime={end_date}&endTimeType={end_time_type}&reason={reason}"
    else:
        complete_url = f"{url}?token={token}&leaveType={leave_type_id}&rangTime={start_time},{end_time}&reason={reason}"

    # 构建最终的成功回复Markdown字符串
    print(leave_data)   
    print(complete_url)
    return complete_url


async def get_default_leave_url():
    url, headers = await get_url_and_headers("vacation")
    token = headers.get("Authorization").split(" ")[1]
    return f"{url}?token={token}"
