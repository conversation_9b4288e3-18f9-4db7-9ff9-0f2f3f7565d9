import httpx
from utils.tool import get_url_and_headers

# 处理考勤列表数据
def process_attendance_list(raw_data: dict):
    columns = [
        {"key": "date", "title": "考勤日期"},
        {"key": "clock_in", "title": "上班签到"},
        {"key": "clock_out", "title": "下班签到"},
        {"key": "absence_length", "title": "缺勤时长"},
        {"key": "status", "title": "考勤状态"},
        {"key": "replenish_status", "title": "补卡状态"},
        {"key": "remark", "title": "备注"}
    ]
    result = []
    for row in raw_data.get("rows", []):
        result.append({
            "date": row.get("checkinDate"),
            "clock_in": row.get("clockBeginTime"),
            "clock_out": row.get("clockEndTime"),
            "absence_length": f"{int(row.get('absenceLength', 0))}分钟" if row.get("absenceLength") is not None else "-",
            "status": row.get("statusDesc", "-"),
            "replenish_status": row.get("replenishStatusDesc", "-"),
            "remark": row.get("remark") or "-"
        })
    return {"columns": columns, "data": result}

# 分页查询考勤信息
async def get_attendance_list(page_num: int = 1, page_size: int = 10):
    url, headers = await get_url_and_headers("get_attendance_list")
    headers = headers.copy()
    params = {"pageNum": page_num, "pageSize": page_size}
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            raw_data = response.json()
    except Exception as e:
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_attendance_list(raw_data) 



# 处理考勤详情数据
def process_daily_Data(raw_data: dict):

    daily_list = []
    raw_data = raw_data.get("data", [])
    # status异常的才加入列表
    for row in raw_data:
        if row.get("status") == "1" or row.get("status") == 1:
            daily_list.append({
                "reportDate": row.get("reportDate"),    # 考勤日期
                "abnormalNum": row.get("abnormalNum"),  # 异常次数
            "unsignedNum": row.get("unsignedNum"),  # 未打卡次数
            "status": row.get("status"),           # 考勤状态
            "statusDesc": row.get("statusDesc"),   # 考勤状态描述
            "abnormalMinuteBeginTime": row.get("abnormalMinuteBeginTime"),      # 异常上班时间
            "missingMinuteBeginTime": row.get("missingMinuteBeginTime"),        # 漏卡上班时间
            "abnormalMinuteEndTime": row.get("abnormalMinuteEndTime"),          # 异常下班时间
            "missingMinuteEndTime": row.get("missingMinuteEndTime"),            # 漏卡下班时间
            "errorDesc": row.get("errorDesc")                                  # 异常描述
            })
    sumAttendanceAbnormalNum = sum(row.get("abnormalNum") for row in raw_data)           # 异常次数总和
    sumUnsignedNum = sum(row.get("unsignedNum") for row in raw_data)          # 未打卡次数总和
    return {"daily_list": daily_list, "sumAttendanceAbnormalNum": sumAttendanceAbnormalNum, "sumUnsignedNum": sumUnsignedNum}


# 查询指定日期的考勤详情
async def get_attendance_daily_report(start_date: str, end_date: str) -> dict:
    url, headers = await get_url_and_headers("get_daily_report")
    headers = headers.copy()
    params = {"startDate": start_date, "endDate": end_date}
    print(url, headers, params)
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            raw_data = response.json()
    except Exception as e:
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_daily_Data(raw_data)
