import httpx
from utils.tool import get_url_and_headers

def process_leave_list(raw_data: dict):
    columns = [
        {"key": "leave_type", "title": "休假项目"},
        {"key": "start_time", "title": "开始时间"},
        {"key": "end_time", "title": "结束时间"},
        {"key": "duration", "title": "时长"},
        {"key": "doc_type", "title": "单据类型"},
        {"key": "reason", "title": "事由"},
        {"key": "audit_status", "title": "审批状态"},
        {"key": "back_status", "title": "销假状态"}
    ]
    result = []
    for row in raw_data.get("rows", []):
        result.append({
            "leave_type": row.get("leaveTypeText", "-"),
            "start_time": row.get("startTime", "-"),
            "end_time": row.get("endTime", "-"),
            "duration": row.get("timeLengthDesc", "-"),
            "doc_type": row.get("documentTypeDesc", "-"),
            "reason": row.get("reason", "-"),
            "audit_status": row.get("auditStatusDesc", "-"),
            "back_status": row.get("backStatusDesc", "-"),
        })
    return {"columns": columns, "data": result}

async def get_leave_list(start_date: str = "2025-04-16", end_date: str = "2025-04-18"):
    url, headers = await get_url_and_headers("get_leave_list")
    headers = headers.copy()
    params = {"pageNum": 1, "pageSize": 10, "startTime": start_date, "endTime": end_date}
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            raw_data = response.json()
    except Exception as e:  
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_leave_list(raw_data) 