import httpx
from utils.tool import get_url_and_headers

def process_month_report(raw_data: dict):
    return raw_data

async def get_month_report():
    url, headers = await get_url_and_headers("get_month_report")
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=headers)
            raw_data = response.json()
    except Exception as e:
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_month_report(raw_data) 