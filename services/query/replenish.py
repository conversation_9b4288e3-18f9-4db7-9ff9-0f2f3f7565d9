import httpx
from utils.tool import get_url_and_headers

def process_replenish_list(raw_data: dict):
    result = []
    for row in raw_data.get("rows", []):
        result.append({
            "replenishTime": row.get("replenishTime", "-"),
            "reason": row.get("reason", "-"),
            "auditStatusDesc": row.get("auditStatusDesc", "-"),
        })
    return {"data": result}


async def get_replenish_list(start_date: str, end_date: str):
    url, headers = await get_url_and_headers("get_replenish_list")
    headers = headers.copy()
    checkinStartTime = start_date + " 00:00:00"
    checkinEndTime = end_date + " 23:59:59"
    params = {"pageNum": 1, "pageSize": 100, "checkinStartTime": checkinStartTime, "checkinEndTime": checkinEndTime}
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            raw_data = response.json()
    except Exception as e:  
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_replenish_list(raw_data) 