import httpx
from utils.tool import get_url_and_headers

def process_trip_list(raw_data: dict):
    result = []
    for row in raw_data.get("rows", []):
        result.append({
            "startTime": row.get("startTime", "-"),
            "endTime": row.get("endTime", "-"),
            "timeLengthDesc": row.get("timeLengthDesc", "-"),
            "reason": row.get("reason", "-"),
            "auditStatusDesc": row.get("auditStatusDesc", "-"),
        })
    return {"data": result}

async def get_trip_list(start_date: str = "2025-04-16", end_date: str = "2025-04-18"):
    url, headers = await get_url_and_headers("get_trip_list")
    headers = headers.copy()
    params = {"pageNum": 1, "pageSize": 100, "startTime": start_date, "endTime": end_date}
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            raw_data = response.json()
    except Exception as e:  
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_trip_list(raw_data) 