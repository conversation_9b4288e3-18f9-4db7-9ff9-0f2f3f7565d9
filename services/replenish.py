from utils.tool import get_url_and_headers
from urllib.parse import quote
import json
import httpx


def process_missing_list(raw_data: dict):
    missing_list = []
    raw_data = raw_data.get("rows", [])
    #将missingCount大于0的加入列表
    for row in raw_data:
        if row.get("missingCount") > 0:
            missing_list.append({
                "date": row.get("date"),
                "missingCount": row.get("missingCount"),
                "missingStandardDateList": row.get("missingStandardDateList"),
                "attendanceMonth": row.get("attendanceMonth"),
            })
    sumMissingCount = sum(row.get("missingCount") for row in raw_data)  
    return {"missing_list": missing_list, "sumMissingCount": sumMissingCount}


# 查询指定日期的缺卡列表
async def get_missing_list(start_date: str, end_date: str) -> dict:
    url, headers = await get_url_and_headers("lack_list")
    headers = headers.copy()
    params = {"startDate": start_date, "endDate": end_date, "pageNum": 1, "pageSize": 50}
    print(url, headers, params)
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.get(url, headers=headers, params=params)
            response.raise_for_status()
            raw_data = response.json()
    except Exception as e:
        print(f"远程接口调用失败: {e}")
        raw_data = {}
    return process_missing_list(raw_data)


# 生成补卡URL
async def submit_and_get_replenish_link(replenish_data: dict) -> str:

    remedylist = replenish_data.get('remedylist')
    datas = []
    for remedy in remedylist:
        date = remedy.get('date')
        reason = remedy.get('reason')
        datas.append({
            "date": date,
            "reason": ",".join(reason)
        })
    datas = json.dumps(datas)
    print(datas)
    url, headers = await get_url_and_headers("replenish")
    token = headers.get("Authorization").split(" ")[1]
    datas = quote(datas)
    complete_url = f"{url}?token={token}&remedylist={datas}"
    print(complete_url)
    return complete_url
