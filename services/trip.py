from utils.tool import get_url_and_headers, split_datatime_str
from urllib.parse import quote
import json


async def submit_and_get_trip_link(trip_data: dict) -> str:
    
    tripList = trip_data.get('tripList')
    trips = []
    for trip in tripList:
        startTime, startTimeType = split_datatime_str(trip.get('startTime'))
        endTime, endTimeType = split_datatime_str(trip.get('endTime'))
        trips.append({
            "startTime": startTime,
            "startTimeType": startTimeType,
            "endTime": endTime,
            "endTimeType": endTimeType,
            "destination": trip.get('destination'),
        })
    trips = json.dumps(trips)
    reason = trip_data.get('reason')
    trips = quote(trips)
    reason = quote(reason)
    url, headers = await get_url_and_headers("evection")
    token = headers.get("Authorization").split(" ")[1]
    complete_url = f"{url}?token={token}&zjBizTripDetailList={trips}&reason={reason}"
    print(complete_url)
    return complete_url