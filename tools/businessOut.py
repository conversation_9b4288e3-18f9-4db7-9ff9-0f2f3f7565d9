from core.mcp_instance import fast_mcp
from services.businessOut import submit_and_get_businessOut_link


@fast_mcp.tool(name='submit_and_get_businessOut_link', description="根据前端传入的参数,生成公务外出URL,以便用户可以直接点击打开公务外出提交的页面")
async def submit_and_get_businessOut_link_tool(businessOut_data: dict) -> str:
    """
    根据前端传入的参数,生成公务外出URL,以便用户可以直接点击打开公务外出提交的页面

    参数:
        输入的参数是一个json字符串,包含以下字段:
        - outList (list, 必须): 外出列表,可以同时包含多个外出记录,每个记录是一个字典,包含以下字段:
            - startTime (str, 必须): 外出开始时间,开始日期和时间，格式为 'YYYY-MM-DD HH:MM:SS'。注意：根据公司规则，时间部分【只能】是 '08:30:00'(上午开始) 或 '13:30:00'(下午开始)。你需要将用户的模糊时间输入（如“早上9点”）对齐到这两个有效时间点之一。
            - endTime (str, 必须): 外出结束时间,结束日期和时间，格式为 'YYYY-MM-DD HH:MM:SS'。注意：根据公司规则，结束时间点通常是 '13:30:00'(上午假结束) 或 '18:00:00'(下午或全天假结束)。
            - destination (str, 必须): 外出目的地
        - reason (str, 必须): 出差事由

    返回:
        str: 构建最终的公务外出申请URL
    """
    print(f'businessOut_data: {businessOut_data}')
    return await submit_and_get_businessOut_link(businessOut_data)


