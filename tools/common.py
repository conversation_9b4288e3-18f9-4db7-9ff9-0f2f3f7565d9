from core.mcp_instance import fast_mcp
from services.common import get_current_user_info


def get_work_times() -> dict:
    """
    返回上班时间点（早晨上班、下午上班、下班），以dict形式返回。
    """
    return {
        "morning_start": "08:30:00",
        "afternoon_start": "13:00:00",
        "work_end": "17:30:00"
    }


#@fast_mcp.tool(name='get_work_times')
async def get_work_times_tool() -> dict:
    """
    获取标准上班时间点（早晨上班、下午上班、下班）。
    返回：
        dict: {"morning_start": "08:30:00", "afternoon_start": "13:00:00", "work_end": "18:00:00"}
    """
    return get_work_times()




#@fast_mcp.tool(name='get_current_user_info')
async def get_current_user_info_tool() -> dict:
    """
    获取当前用户信息。
    返回：
        dict: {"user_id": "1234567890", "user_name": "张三", "user_email": "<EMAIL>"}
    """
    return get_current_user_info()
