from services.leave import get_leave_type_list, get_process_type_list
from core.mcp_instance import fast_mcp
from services.leave import submit_and_get_link
from services.leave import get_default_leave_url

@fast_mcp.tool(name='get_leave_type_list', description='获取请假类型列表')
async def get_leave_type_list_tool() -> dict:
    """
    获取请假类型列表。

    该函数会调用后端服务，获取所有请假类型的简要信息, 并返回请假类型ID, 请假类型名称, 请假类型描述及剩余额度, 假期余额。

    参数：
        无

    返回：
        一个数组，每个元素为一个字典，包含：
            - balanceDesc: 请假类型的描述及剩余额度(str)。
            - balance: 假期余额(float)。
            - leaveTypeDesc: 请假类型描述(str)。
        例如：
        [
            {"balanceDesc": "调休假(剩余0.0小时)", "balance": 0.0, "leaveTypeDesc": "调休假"},
            {"balanceDesc": "年休假(剩余4.0天)", "balance": 4.0, "leaveTypeDesc": "年休假"}      
        ]
    """
    return await get_leave_type_list()


@fast_mcp.tool(name='get_process_type_list')
async def get_process_type_list_tool() -> dict:
    """
    获取请假流程类型列表。

    该函数会调用后端服务，获取所有请假流程类型的简要信息, 并返回请假流程类型名称。

    参数：
        无

    返回：
        一个数组，每个元素为一个字典，仅包含：
            - processTypeDesc: 流程类型的名称(str)
        例如：
        [
            {"processTypeDesc": "总部M8-2以下"},
            {"processTypeDesc": "总部M8-2及以上"}
        ]
    """
    return await get_process_type_list()


@fast_mcp.tool(name='submit_and_get_leave_link', description="根据前端传入的参数,生成请假URL,以便用户可以直接点击打开请假提交的页面")
async def submit_and_get_leave_link_tool(leave_data: dict) -> str:
    """
    根据前端传入的参数,生成请假URL,以便用户可以直接点击打开请假提交的页面

    参数:
        输入的参数是一个json字符串,包含以下字段:
        - leaveTypeDesc (str, 必须): 请假类型描述。这个通过调用`get_leave_type_list`工具,并根据用户的选择从返回结果中准确提取leaveTypeDesc这个字段。严禁使用"年假"等中文字符串。
        - startTime (str, 必须): 请假的开始日期和时间，格式为 'YYYY-MM-DD HH:MM:SS'。注意：根据公司规则，时间部分【只能】是 '08:30:00'(上午开始) 或 '13:30:00'(下午开始)。你需要将用户的模糊时间输入（如“早上9点”）对齐到这两个有效时间点之一,但调休假不需要对齐到这两个时间。哺乳假只需要提供日期即可如：2025-07-01，不需要具体时间HH:MM:SS。
        - endTime (str, 必须): 请假的结束日期和时间，格式为 'YYYY-MM-DD HH:MM:SS'。注意：根据公司规则，结束时间点通常是 '13:30:00'(上午假结束) 或 '18:00:00'(下午或全天假结束)，但调休假不需要对齐到这两个时间。哺乳假只需要提供日期即可如：2025-07-01，不需要具体时间HH:MM:SS。
        - reason (str, 必须): 用户陈述的请假事由，需完整记录。

    返回:
        str: 构建最终的请假URL
    """

    print(leave_data)
    # 参数完整性校验，缺失的参数补None
    required_fields = [
        'leaveTypeDesc', 'startTime', 'endTime',
        'reason'
    ]   

    for field in required_fields:
        if field not in leave_data:
            leave_data[field] = ""
            
    return await submit_and_get_link(leave_data)

@fast_mcp.tool(name='get_default_leave_url')
def get_default_leave_url_tool() -> str:
    """
    获取默认的请假URL, 前端无需传入参数, 直接调用此工具获取默认的请假URL,当用户参数不完整时, 会返回默认的请假URL

    参数:
        无

    返回:
        str: 默认的请假URL
    """


    return get_default_leave_url()