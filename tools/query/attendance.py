from services.query.attendance import get_attendance_list
from core.mcp_instance import fast_mcp
from services.query.attendance import get_attendance_daily_report

#@fast_mcp.tool(name='get_attendance_list', description='分页查询考勤信息')
async def get_attendance_list_tool(page_num: int = 1, page_size: int = 10) -> dict:
    """
    分页查询考勤信息。

    参数：
      - page_num: 页码，默认为1
      - page_size: 每页数量，默认为10

    返回：
    {
        "columns": [
            {"key": "date", "title": "考勤日期"},
            {"key": "clock_in", "title": "上班签到"},
            {"key": "clock_out", "title": "下班签到"},
            {"key": "absence_length", "title": "缺勤时长"},
            {"key": "status", "title": "考勤状态"},
            {"key": "replenish_status", "title": "补卡状态"},
            {"key": "remark", "title": "备注"}
        ],
        "data": [
            {
                "date": "2025-06-13",
                "clock_in": null,
                "clock_out": null,
                "absence_length": "450分钟",
                "status": "异常",
                "replenish_status": "-",
                "remark": "-"
            },
            ...
        ]
    }
    """
    return await get_attendance_list(page_num, page_size) 


@fast_mcp.tool(name='get_attendance_daily_report', description='查询指定日期的考勤详情')
async def get_attendance_daily_report_tool(start_date: str = "2025-04-16", end_date: str = "2025-04-18") -> dict:
    """
    查询指定日期的考勤详情。    

    参数：
        - start_date: 开始日期，格式为YYYY-MM-DD
        - end_date: 结束日期，格式为YYYY-MM-DD

    返回：  
    {
        "sumAttendanceAbnormalNum": 1,   # 迟到/早退次数总和
        "sumUnsignedNum": 1,   # 未打卡次数总和
        "daily_list": [
            {
                "reportDate": "2025-06-13",  # 考勤日期
                "abnormalNum": 1,   # 迟到/早退次数
                "unsignedNum": 1,   # 未打卡次数
                "status": "1",      # 考勤状态 0: 正常 1: 异常
                "statusDesc": "异常",   # 考勤状态描述
                "abnormalMinuteBeginTime": "2025-04-17 08:31:00",   # 上班迟到时间
			    "missingMinuteBeginTime": "2025-04-17 09:00:00",   # 上班半天缺卡时间   
			    "abnormalMinuteEndTime": "2025-04-17 17:29:00",   # 下班早退时间        
			    "missingMinuteEndTime": "2025-04-17 17:00:00",   # 下班半天缺卡时间         
            },
            ...
        ],
    }

    说明：
    1. daily_list 如果为空，则说明当天考勤正常，无迟到/早退/缺卡
    2. sumAttendanceAbnormalNum 为迟到/早退次数总和，即为有迟到和早退的情况
    3. sumUnsignedNum 为未打卡次数总和，即为有未打卡的情况
    4. 如果查不到考勤记录，那考勤就是正常，需要回复用户考勤正常
    """    
    return await get_attendance_daily_report(start_date, end_date)
    
