from services.query.leave import get_leave_list
from core.mcp_instance import fast_mcp

@fast_mcp.tool(name='get_leave_list', description="查询指定日期范围内的请假列表")
async def get_leave_list_tool(start_date: str = "2025-04-16", end_date: str = "2025-04-18") -> dict:
    """
    查询指定日期范围内的请假列表。

    参数：
      - start_date: 开始日期，格式为YYYY-MM-DD
      - end_date: 结束日期，格式为YYYY-MM-DD

    返回：
    {
        "columns": [
            {"key": "leave_type", "title": "休假项目"},
            {"key": "start_time", "title": "开始时间"},
            {"key": "end_time", "title": "结束时间"},
            {"key": "duration", "title": "时长"},
            {"key": "doc_type", "title": "单据类型"},
            {"key": "reason", "title": "事由"},
            {"key": "audit_status", "title": "审批状态"},
            {"key": "back_status", "title": "销假状态"}
        ],
        "data": [
            {
                "leave_type": "年休假",
                "start_time": "2025-04-18 08:30:00",
                "end_time": "2025-04-18 18:00:00",
                "duration": "1.0天",
                "doc_type": "请假",
                "reason": "11",
                "audit_status": "待发起",
                "back_status": "未销假"
            },
            ...
        ]
    }
    """
    return await get_leave_list(start_date, end_date) 