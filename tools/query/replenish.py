from services.query.replenish import get_replenish_list
from core.mcp_instance import fast_mcp

@fast_mcp.tool(name='get_replenish_list', description="查询指定日期范围内的补卡列表")
async def get_replenish_list_tool(start_date: str = "2025-04-16", end_date: str = "2025-04-18") -> dict:
    """
    查询指定日期范围内的补卡列表。

    参数：
      - start_date: 开始日期，格式为YYYY-MM-DD
      - end_date: 结束日期，格式为YYYY-MM-DD

    返回：
    {
        "data": [
            {
                "replenishTime": "2025-04-18 08:30:00",   补卡点
                "reason": "出差",      补卡事由
                "auditStatusDesc": "待发起",  审批状态
            },
            ...
        ]
    }
    """
    return await get_replenish_list(start_date, end_date) 