from services.query.trip import get_trip_list
from core.mcp_instance import fast_mcp

@fast_mcp.tool(name='get_trip_list', description="查询指定日期范围内的出差列表")
async def get_trip_list_tool(start_date: str = "2025-04-16", end_date: str = "2025-04-18") -> dict:
    """
    查询指定日期范围内的出差列表。

    参数：
      - start_date: 开始日期，格式为YYYY-MM-DD
      - end_date: 结束日期，格式为YYYY-MM-DD

    返回：
    {
        "data": [
            {
                "startTime": "2025-04-18 08:30:00",  出差开始时间
                "endTime": "2025-04-18 18:00:00",  出差结束时间
                "timeLengthDesc": "1.0天",  出差时长
                "reason": "出差",  出差事由
                "auditStatusDesc": "待发起",  审批状态
            },
            ...
        ]
    }
    """
    return await get_trip_list(start_date, end_date) 