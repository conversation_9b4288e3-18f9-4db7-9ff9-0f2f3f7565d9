from core.mcp_instance import fast_mcp
from services.replenish import get_missing_list, submit_and_get_replenish_link


@fast_mcp.tool(name='get_missing_list', description='按照指定日期，查询未打卡列表')
async def get_missing_list_tool(start_date: str = "2025-04-16", end_date: str = "2025-04-18") -> dict:
    """
    按照指定日期，查询未打卡列表。    

    参数：
        - start_date: 开始日期，格式为YYYY-MM-DD
        - end_date: 结束日期，格式为YYYY-MM-DD

    返回：  
    {
        "sumMissingCount": 1,   # 未打卡次数总和
        "missing_list": [
            {
                "date": "2025-06-13",  # 缺卡日期
                "missingCount": 2,     # 当前日期缺卡次数
                "missingStandardDateList": [
                    "2025-07-07 08:30:00",   
                    "2025-07-07 17:30:00"
                ],      # 缺卡标准时间列表
                "attendanceMonth": "7",    # 考勤月份    
            },
            ...
        ],
    }

    说明：
    1. missing_list 如果为空，则说明没有未打卡的情况
    2. sumMissingCount 为未打卡次数总和，即为有未打卡的情况
    """    
    return await get_missing_list(start_date, end_date)



@fast_mcp.tool(name='submit_and_get_replenish_link', description="根据前端传入的参数,生成补卡的URL,以便用户可以直接点击打开补卡提交的页面")
async def submit_and_get_replenish_link_tool(replenish_data: dict) -> str:
    """
    根据前端传入的参数,生成补卡URL,以便用户可以直接点击打开补卡提交的页面

    参数:
        输入的参数是一个json字符串,包含以下字段:
        - remedylist (list, 必须): 补卡列表,包含多个补卡记录,每个记录是一个字典,包含以下字段:
            - date (str, 必须): 补卡日期来自于get_missing_list的返回结果中的missing_list中的date字段，格式为YYYY-MM-DD
            - reason (list, 必须): 补卡事由列表,如果一天有两次缺卡则包含两个补卡事由,每个事由是一个字符串，如果一天只有一次缺卡则包含一个补卡事由

        例如:
        1、补卡
        {
            "remedylist": [
                {
                    "date": "2025-07-08",
                    "reason": ["忘记打卡","忘记打卡"]
                },
                {
                    "date": "2025-07-07",
                    "reason": ["忘记打卡"]
                }
            ]
        }
    返回:
        str: 构建最终的补卡的URL
    """
    print(f'replenish_data: {replenish_data}')
    return await submit_and_get_replenish_link(replenish_data)

