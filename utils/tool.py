from config.config import EXTERNAL_SYSTEMS
from functools import wraps
import ast
import json
from fastmcp.server.dependencies import get_http_headers, get_http_request
import httpx


#根据username获取token
async def get_token(username: str):
    base_url = EXTERNAL_SYSTEMS["base_url"]
    path = EXTERNAL_SYSTEMS["report"]["api"]["token"]
    url = f"{base_url}{path}"
    headers = EXTERNAL_SYSTEMS["headers"]
    body_data = {
        "username": username,
        "appkey": EXTERNAL_SYSTEMS["appkey"]
    }
    print(f'get_token: url: {url}, headers: {headers}, body_data: {body_data}')
    # 使用httpx发送请求
    try:
        async with httpx.AsyncClient(verify=False) as client:
            response = await client.post(url, headers=headers, json=body_data)
            response.raise_for_status()
            token = response.json()["data"]["access_token"]
            print(token)
            return token
    except Exception as e:
        return ""

#根据接口参数获取url和headers
async def get_url_and_headers(api_name: str):
    #获取url中的参数
    request = get_http_request()
    query_params = request.query_params
    user_id = query_params.get("user_id")
    base_url = EXTERNAL_SYSTEMS["base_url"]
    path = EXTERNAL_SYSTEMS["report"]["api"][api_name]
    url = f"{base_url}{path}"
    headers = EXTERNAL_SYSTEMS["headers"]
    http_headers = get_http_headers()
    token = http_headers.get("authorization")
    print(f'raw_token: {token}')
    #如果token不是Bearer开头，则添加Bearer前缀
    if(token and token.startswith("Bearer ")):
        token = token.split(" ")[1]
        headers["Authorization"] = f"Bearer {token}"
    else:
        #如果token不是Bearer开头，则添加Bearer前缀
        headers["Authorization"] = f"Bearer {token}"
    #print(f'target_token: {headers["Authorization"]}')
    return url, headers

# 注入token
def inject_token(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        context = kwargs.get("context")
        token = None
        if context and hasattr(context, "headers"):
            token = context.headers.get("Authorization")
        kwargs["token"] = token
        return await func(*args, **kwargs)
    return wrapper



# 处理大模型返回的dict或单引号字符串，转为标准dict，所有key和value都为str类型，None变成空字符串。
def normalize_json_dict(data):
    # 创建一个新字典来存储替换后的结果
    new_dict = {}
    json_data = json.dumps(data, indent=4, ensure_ascii=False)
    json_data = json.loads(json_data)
    # 遍历原始字典
    for key, value in data.items():
        # 检查键是否为字符串类型
        if isinstance(key, str):
            new_key = f'"{key}"'
            new_key = key.replace("'", '"')
        else:
            new_key = key  # 如果不是字符串，则保持原样
        
        # 检查值是否为字符串类型
        if isinstance(value, str):
           # f'"{single_quoted_string}"'
            new_value = value.replace("'", '"')
        else:
            new_value = value  # 如果不是字符串，则保持原样
        
        # 将替换后的键值对存入新字典
        new_dict[new_key] = new_value
    return new_dict

# 格式化时间到HH:MM:SS
def time_format(time_str: str):
    if len(time_str.split(':')) == 2:
        time_str = time_str.split(':')[0].zfill(2) + ':' + time_str.split(':')[1].zfill(2) + ':00'
    elif len(time_str.split(':')) == 1:
        time_str = time_str.split(':')[0].zfill(2) + ':00:00'
    else:
        time_str = time_str.split(':')[0].zfill(2) + ':' + time_str.split(':')[1].zfill(2) + ':' + time_str.split(':')[2].zfill(2)  
    return time_str

def split_datatime_str(date_str: str):
    #将date_str分为date和time两部分，分别赋值给start_date和start_time
    start_date = date_str.split(' ')[0]
    start_time = date_str.split(' ')[1]
    return start_date, start_time
